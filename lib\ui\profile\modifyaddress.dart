import 'package:db_eats/widgets/location_search_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:db_eats/bloc/account_bloc.dart';
import 'package:db_eats/data/models/guesthome/viewaddressmodel.dart';
import 'dart:async';
import 'dart:developer' as developer;


class AddressData {
  final String houseNumber;
  final String streetAddress;
  final String city;
  final String state;
  final String zipCode;
  final String phoneNumber;
  final String landmark;
  final LatLng? location;

  AddressData({
    required this.houseNumber,
    required this.streetAddress,
    required this.city,
    required this.state,
    required this.zipCode,
    required this.phoneNumber,
    required this.landmark,
    this.location,
  });
}

class ModifyAddressPage extends StatefulWidget {
  final String type;
  final int? addressId;

  const ModifyAddressPage({
    super.key,
    required this.type,
    this.addressId,
  });

  @override
  State<ModifyAddressPage> createState() => _ModifyAddressPageState();
}

class _ModifyAddressPageState extends State<ModifyAddressPage> {
  late double ten;
  late double three;
  late double six;
  late double nine;
  late double eight;
  late double twelve;
  late double forteen;
  late double sixteen;
  late double eighteen;
  late double twenty;
  late double twentyFour;
  late double screenWidth;
  late double screenHeight;

  final List<String> buildingTypes = [
    'House',
    'Apartment',
    'Office',
    'Condo',
    'Other',
  ];
  String? buildingType;

  final Completer<GoogleMapController> _controller = Completer();
  LatLng currentLatLng =
      const LatLng(37.7749, -122.4194); // Default to San Francisco
  String currentAddress = '';
  String selectedCoordinates = '';
  final Set<Marker> _markers = {};
  bool adjustPinMode = false;
  late final Set<Factory<OneSequenceGestureRecognizer>> gestureRecognizers;

  final String _googleApiKey = "AIzaSyCpAdQaZ3fPe5H0wfkI0NqMXcT8J7AW9uY";
  final TextEditingController _searchController = TextEditingController();

  double? selectedLatitude;
  double? selectedLongitude;

  // Text controllers
  final TextEditingController _houseNumberController = TextEditingController();
  final TextEditingController _streetAddressController =
      TextEditingController();
  final TextEditingController _cityController = TextEditingController();
  final TextEditingController _stateController = TextEditingController();
  final TextEditingController _zipCodeController = TextEditingController();
  final TextEditingController _phoneNumberController = TextEditingController();
  final TextEditingController _landmarkController = TextEditingController();

  bool _isSaving = false;

  bool _isCurrentAddress = false;

  int? _currentAddressId;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;
    ten = screenWidth * 0.02545;
    three = screenWidth * 0.00763;
    six = screenWidth * 0.01886;
    nine = screenWidth * 0.02304;
    eight = screenWidth * 0.02063;
    twelve = screenWidth * 0.03054;
    forteen = screenWidth * 0.035;
    sixteen = screenWidth * 0.04073;
    eighteen = screenWidth * 0.04582;
    twenty = screenWidth * 0.05091;
    twentyFour = screenWidth * 0.06109;
  }

  @override
  void initState() {
    super.initState();

    gestureRecognizers = {
      Factory<PanGestureRecognizer>(() => PanGestureRecognizer()),
      Factory<ScaleGestureRecognizer>(() => ScaleGestureRecognizer()),
      Factory<TapGestureRecognizer>(() => TapGestureRecognizer()),
      Factory<VerticalDragGestureRecognizer>(
          () => VerticalDragGestureRecognizer()),
    };

    if (widget.type == 'edit' && widget.addressId != null) {
      context.read<AccountBloc>().add(ViewAddressEvent(widget.addressId!));
    } else {
      _markers.add(
        Marker(
          markerId: const MarkerId('current'),
          position: currentLatLng,
          icon:
              BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen),
          infoWindow: InfoWindow(
            title: 'Current Location',
            snippet: 'Default location',
          ),
        ),
      );
    }
  }

  @override
  void dispose() {
    _houseNumberController.dispose();
    _streetAddressController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _zipCodeController.dispose();
    _phoneNumberController.dispose();
    _landmarkController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Widget _buildLabel(String text) {
    return Padding(
      //  padding: EdgeInsets.only(bottom: ten, left: eighteen),
      padding: EdgeInsets.only(bottom: ten, left: 0),
      child: Text(
        text,
        style: TextStyle(
          fontFamily: 'Inter',
          fontWeight: FontWeight.w500,
          fontSize: forteen,
          height: 16 / forteen,
          letterSpacing: 0.02,
        ),
      ),
    );
  }

  Widget _buildTextField(String hintText,
      {TextEditingController? controller,
      int maxLines = 1,
      bool isPhoneNumber = false}) {
    return Container(
      margin: EdgeInsets.only(bottom: twenty),
      padding: EdgeInsets.symmetric(horizontal: eighteen),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: const Color(0xFFE1E3E6)),
        borderRadius: BorderRadius.circular(maxLines > 2 ? 10 : 35),
      ),
      child: SizedBox(
        height: maxLines == 1 ? ten * 4 : null,
        child: TextField(
          controller: controller,
          maxLines: maxLines,
          keyboardType:
              isPhoneNumber ? TextInputType.number : TextInputType.text,
          inputFormatters:
              isPhoneNumber ? [FilteringTextInputFormatter.digitsOnly] : null,
          style: TextStyle(fontSize: sixteen, height: 1.3),
          decoration: InputDecoration(
            isDense: true,
            contentPadding: EdgeInsets.symmetric(vertical: ten),
            hintText: hintText,
            border: InputBorder.none,
            hintStyle: TextStyle(
              fontFamily: 'Inter',
              fontSize: sixteen,
              height: 1.3,
              color: Color(0xFFAAADB1),
            ),
          ),
        ),
      ),
    );
  }

  void _onSavePressed() async {
    if (_houseNumberController.text.trim().isEmpty ||
        _streetAddressController.text.trim().isEmpty ||
        _cityController.text.trim().isEmpty ||
        _stateController.text.trim().isEmpty ||
        selectedLatitude == null ||
        selectedLongitude == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fill all required fields and select location'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    String addressText;
    if (currentAddress.isNotEmpty) {
      addressText = currentAddress;
    } else {
      List<String> addressParts = [];
      if (_streetAddressController.text.trim().isNotEmpty) {
        addressParts.add(_streetAddressController.text.trim());
      }
      if (_cityController.text.trim().isNotEmpty) {
        addressParts.add(_cityController.text.trim());
      }
      addressText = addressParts.join(', ');

      if (addressText.isEmpty) {
        addressText = 'Selected location';
      }
    }

    if (widget.type == 'edit' && _currentAddressId != null) {
      bool shouldBeCurrent = _isCurrentAddress;

      if (!_isCurrentAddress) {
        final result = await _showMakeCurrentDialog();
        if (result == null) return;
        shouldBeCurrent = result;
      }

      final addressData = {
        "id": _currentAddressId,
        "latitude": selectedLatitude ?? currentLatLng.latitude,
        "longitude": selectedLongitude ?? currentLatLng.longitude,
        "address_text": addressText,
        "building_type": buildingType ?? "House",
        "house_number": _houseNumberController.text.trim(),
        "landmark": _landmarkController.text.trim().isEmpty
            ? "Near ${_cityController.text.trim()}"
            : _landmarkController.text.trim(),
        "is_current": shouldBeCurrent
      };

      context.read<AccountBloc>().add(EditAddressEvent(addressData));
    } else {
      final addressData = {
        "latitude": selectedLatitude ?? currentLatLng.latitude,
        "longitude": selectedLongitude ?? currentLatLng.longitude,
        "address_text": addressText,
        "building_type": buildingType ?? "House",
        "house_number": _houseNumberController.text.trim(),
        "landmark": _landmarkController.text.trim().isEmpty
            ? "Near ${_cityController.text.trim()}"
            : _landmarkController.text.trim(),
        "is_current": true
      };

      context.read<AccountBloc>().add(AddAddressEvent(addressData));
    }
  }

  void _onMapCreated(GoogleMapController controller) {
    developer.log('Map controller being created');
    _controller.complete(controller);
    _testApiKey();
  }

  Future<void> _testApiKey() async {
    try {
      await _controller.future;
      developer.log('Map controller ready - API key working');
    } catch (e) {
      developer.log('Map initialization error: $e');
    }
  }

  void _onMapTap(LatLng position) {
    setState(() {
      currentLatLng = position;
      selectedLatitude = position.latitude;
      selectedLongitude = position.longitude;
      _markers.clear();

      // Choose marker color based on mode
      BitmapDescriptor markerIcon;
      String markerTitle;
      String markerSnippet;

      if (adjustPinMode) {
        markerIcon =
            BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue);
        markerTitle = 'Adjusting Pin';
        markerSnippet = 'Tap "Fix Pin" to confirm location';
      } else {
        markerIcon =
            BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue);
        markerTitle = 'Selected Location';
        markerSnippet =
            'Lat: ${position.latitude.toStringAsFixed(4)}, Lng: ${position.longitude.toStringAsFixed(4)}';
      }

      _markers.add(
        Marker(
          markerId: const MarkerId('tapped'),
          position: position,
          icon: markerIcon,
          infoWindow: InfoWindow(
            title: markerTitle,
            snippet: markerSnippet,
          ),
        ),
      );
    });

    // Always update address and auto-fill fields when pin is moved
    _updateAddress(position);

    // Show confirmation message only in normal mode
    if (!adjustPinMode) {
      // ScaffoldMessenger.of(context).showSnackBar(
      //   SnackBar(
      //     content: Text(
      //         'Coordinates: ${position.latitude.toStringAsFixed(4)}, ${position.longitude.toStringAsFixed(4)}'),
      //     duration: Duration(seconds: 2),
      //     backgroundColor: Color(0xFF1F2122),
      //   ),
      // );
    }
  }

  Future<void> _updateAddress(LatLng position) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        setState(() {
          selectedCoordinates = '${position.latitude}, ${position.longitude}';

          // Build address text with only non-empty parts
          List<String> addressParts = [];
          if (place.street?.isNotEmpty == true) {
            addressParts.add(place.street!);
          }
          if (place.subLocality?.isNotEmpty == true) {
            addressParts.add(place.subLocality!);
          }
          if (place.locality?.isNotEmpty == true) {
            addressParts.add(place.locality!);
          }

          currentAddress = addressParts.join(', ');
          if (currentAddress.isEmpty) {
            currentAddress = 'Selected location';
          }

          // Auto-fill form fields
          _autoFillAddressFields(place);
        });
      }
    } catch (e) {
      setState(() {
        selectedCoordinates = '${position.latitude}, ${position.longitude}';
        currentAddress = 'Location not found';
      });
    }
  }

  void _onCameraMove(CameraPosition position) {
    // Camera move handling is now simplified since we allow tap-to-place in adjust mode
    // This method can be used for other camera-related functionality if needed in the future
  }

  Widget _buildMapSection() {
    return Container(
      margin: EdgeInsets.only(bottom: twenty),
      height: screenHeight * 0.5,
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(12)),
      ),
      child: ClipRRect(
        borderRadius: const BorderRadius.all(Radius.circular(12)),
        child: Stack(
          children: [
            GoogleMap(
              gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>[
                new Factory<OneSequenceGestureRecognizer>(
                  () => new EagerGestureRecognizer(),
                ),
              ].toSet(),
              onMapCreated: _onMapCreated,
              markers: _markers,
              onCameraMove: _onCameraMove,
              onTap: _onMapTap,
              scrollGesturesEnabled: true,
              zoomGesturesEnabled: true,
              rotateGesturesEnabled: true,
              tiltGesturesEnabled: true,
              mapType: MapType.normal,
              myLocationEnabled: true,
              initialCameraPosition: CameraPosition(
                target: currentLatLng,
                zoom: 15,
                tilt: 20.0,
              ),
              zoomControlsEnabled: true,
              myLocationButtonEnabled: false,
              mapToolbarEnabled: false,
              compassEnabled: false,
              buildingsEnabled: false,
              minMaxZoomPreference: const MinMaxZoomPreference(3, 20),
            ),
            // Adjust Pin button
            Positioned(
              bottom: sixteen,
              left: 0,
              right: 0,
              child: Center(
                child: Container(
                  decoration: BoxDecoration(
                    color: const Color(0xFF1F2122),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(25),
                      onTap: () {
                        setState(() {
                          if (adjustPinMode) {
                            // Fix the pin - exit adjust mode
                            adjustPinMode = false;

                            // Update address for current pin position
                            if (_markers.isNotEmpty) {
                              _updateAddress(_markers.first.position);
                              currentLatLng = _markers.first.position;
                              selectedLatitude =
                                  _markers.first.position.latitude;
                              selectedLongitude =
                                  _markers.first.position.longitude;
                            }

                            // Update marker to show it's fixed
                            _markers.clear();
                            _markers.add(
                              Marker(
                                markerId: const MarkerId('fixed'),
                                position: currentLatLng,
                                icon: BitmapDescriptor.defaultMarkerWithHue(
                                    BitmapDescriptor.hueOrange),
                                infoWindow: InfoWindow(
                                  title: 'Fixed Location',
                                  snippet: 'Pin location saved',
                                ),
                              ),
                            );

                            // Show confirmation message
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content:
                                    Text('Pin location fixed successfully!'),
                                duration: Duration(seconds: 2),
                                backgroundColor: Color(0xFF1F2122),
                              ),
                            );
                          } else {
                            // Enter adjust mode
                            adjustPinMode = true;

                            // Show current marker in adjust mode
                            _markers.clear();
                            _markers.add(
                              Marker(
                                markerId: const MarkerId('adjustable'),
                                position: currentLatLng,
                                icon: BitmapDescriptor.defaultMarkerWithHue(
                                    BitmapDescriptor.hueBlue),
                                infoWindow: InfoWindow(
                                  title: 'Adjusting Pin',
                                  snippet: 'Tap anywhere on map to move pin',
                                ),
                              ),
                            );

                            // Show instruction message
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                    'Tap anywhere on the map to adjust pin location'),
                                duration: Duration(seconds: 3),
                                backgroundColor: Color(0xFF1F2122),
                              ),
                            );
                          }
                        });
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: eighteen,
                          vertical: ten,
                        ),
                        child: Text(
                          adjustPinMode ? 'Fix Pin' : 'Adjust Pin',
                          style: TextStyle(
                            color: Colors.white,
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w600,
                            fontSize: forteen,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _autoFillAddressFields(Placemark place) {
    if (place.street != null && place.street!.isNotEmpty) {
      _streetAddressController.text = place.street!;
    } else if (place.thoroughfare != null && place.thoroughfare!.isNotEmpty) {
      _streetAddressController.text = place.thoroughfare!;
    }

    if (place.locality != null && place.locality!.isNotEmpty) {
      _cityController.text = place.locality!;
    } else if (place.subAdministrativeArea != null &&
        place.subAdministrativeArea!.isNotEmpty) {
      _cityController.text = place.subAdministrativeArea!;
    } else if (place.subLocality != null && place.subLocality!.isNotEmpty) {
      _cityController.text = place.subLocality!;
    }

    if (place.administrativeArea != null &&
        place.administrativeArea!.isNotEmpty) {
      _stateController.text = place.administrativeArea!;
    } else if (place.country != null && place.country!.isNotEmpty) {
      _stateController.text = place.country!;
    }

    if (place.postalCode != null && place.postalCode!.isNotEmpty) {
      _zipCodeController.text = place.postalCode!;
    }
  }

  // Get current location
  Future<void> _getCurrentLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Location services are disabled.'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Location permissions are denied'),
              backgroundColor: Colors.red,
            ),
          );
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Location permissions are permanently denied'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      LatLng newLocation = LatLng(position.latitude, position.longitude);

      setState(() {
        currentLatLng = newLocation;
        selectedLatitude = position.latitude;
        selectedLongitude = position.longitude;
        _markers.clear();
        _markers.add(
          Marker(
            markerId: const MarkerId('current'),
            position: newLocation,
            icon: BitmapDescriptor.defaultMarkerWithHue(
                BitmapDescriptor.hueGreen),
            infoWindow: InfoWindow(
              title: 'Current Location',
              snippet: 'Your current location',
            ),
          ),
        );
      });

      // Move camera to current location
      final GoogleMapController controller = await _controller.future;
      await controller.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: newLocation,
            zoom: 16,
            tilt: 20.0,
          ),
        ),
      );

      // Update address
      await _updateAddress(newLocation);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Current location found!'),
          duration: Duration(seconds: 2),
          backgroundColor: Color(0xFF1F2122),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error getting current location: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Widget _buildSearchBar() {
    return LocationSearchField(
      googleApiKey: _googleApiKey,
      onLocationSelected: (address, lat, lng) async {
        setState(() {
          currentLatLng = LatLng(lat, lng);
          selectedLatitude = lat;
          selectedLongitude = lng;
          _markers.clear();
          _markers.add(
            Marker(
              markerId: const MarkerId('searched'),
              position: currentLatLng,
              icon: BitmapDescriptor.defaultMarkerWithHue(
                  BitmapDescriptor.hueRed),
              infoWindow: InfoWindow(
                title: 'Selected Location',
                snippet: address,
              ),
            ),
          );
        });
  
        final GoogleMapController controller = await _controller.future;
        await controller.animateCamera(
          CameraUpdate.newCameraPosition(
            CameraPosition(
              target: currentLatLng,
              zoom: 16,
              tilt: 20.0,
            ),
          ),
        );
  
        await _updateAddress(currentLatLng);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Location plotted on map successfully!'),
            duration: Duration(seconds: 2),
            backgroundColor: Color(0xFF1F2122),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AccountBloc, AccountState>(
      listener: (context, state) {
        if (state is ViewAddressSuccess) {
          // Populate UI with fetched address data
          _populateAddressData(state.data);
        } else if (state is ViewAddressFailed) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
            ),
          );
        }
      },
      child: BlocBuilder<AccountBloc, AccountState>(builder: (context, state) {
        if (widget.type == 'edit' && state is ViewAddressLoading) {
          return Scaffold(
            backgroundColor: const Color(0xFFF6F3EC),
            appBar: AppBar(
              backgroundColor: const Color(0xFFF6F3EC),
              elevation: 0,
              scrolledUnderElevation: 0,
              centerTitle: false,
              leading: IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.black),
                onPressed: () => Navigator.pop(context),
              ),
              title: Text(
                'Edit Address',
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w600,
                  fontSize: eighteen,
                  height: 1.28,
                  letterSpacing: 0,
                  color: Colors.black,
                ),
              ),
            ),
            body: const Center(
              child: CircularProgressIndicator(),
            ),
          );
        }

        return GestureDetector(
          child: Scaffold(
            backgroundColor: const Color(0xFFF6F3EC),
            resizeToAvoidBottomInset: true,
            appBar: AppBar(
              backgroundColor: const Color(0xFFF6F3EC),
              elevation: 0,
              centerTitle: false,
              leading: IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.black),
                onPressed: () => Navigator.pop(context),
              ),
              title: Text(
                widget.type == 'edit' ? 'Edit Address' : 'New Address',
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w600,
                  fontSize: eighteen,
                  height: 1.28,
                  letterSpacing: 0,
                  color: Colors.black,
                ),
              ),
            ),
            body: SingleChildScrollView(
              padding: EdgeInsets.only(
                  left: twenty, right: twenty, bottom: twenty, top: ten * 0.2),
              child: Card(
                elevation: 0,
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: EdgeInsets.all(twenty),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Search bar

                      // Map section
                      _buildMapSection(),
                      _buildLabel('Enter Your Street and House Number'),

                      _buildSearchBar(),
                      SizedBox(height: ten),

                      Divider(
                        color: const Color(0xFFE1E3E6),
                        thickness: 1,
                        height: ten,
                      ),

                      SizedBox(height: ten),

                      _buildLabel('Building Type'),

                      // SizedBox(height: ten),
                      Container(
                        width: double.infinity,
                        height: 40,
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: const Color(0xFFE1E3E6),
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(24),
                          color: Colors.white,
                        ),
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: twelve),
                          child: DropdownButton<String>(
                            value: buildingType,
                            isExpanded: true,
                            icon: Image.asset(
                              'assets/icons/chevron-down.png',
                              width: 18,
                              height: 18,
                            ),
                            underline: const SizedBox(),
                            style: TextStyle(
                              color: const Color(0xFF1F2122),
                              fontSize: 20,
                              fontFamily: 'Inter',
                            ),
                            dropdownColor: Colors.white,
                            items: buildingTypes
                                .map<DropdownMenuItem<String>>((String value) {
                              return DropdownMenuItem<String>(
                                value: value,
                                child: Text(
                                  value,
                                  style: TextStyle(
                                    color: const Color(0xFF66696D),
                                    fontSize: 15,
                                    fontFamily: 'Inter',
                                  ),
                                ),
                              );
                            }).toList(),
                            onChanged: (String? newValue) {
                              setState(() {
                                buildingType = newValue!;
                              });
                            },
                          ),
                        ),
                      ),

                      SizedBox(height: forteen),

                      // // Address display section
                      // _buildAddressDisplay(),

                      _buildLabel('House / Apt / Uhnit / Floor Number'),
                      _buildTextField(
                        'Enter house number',
                        controller: _houseNumberController,
                      ),

                      _buildLabel('Street Address'),
                      _buildTextField(
                        'Enter street address',
                        controller: _streetAddressController,
                      ),

                      _buildLabel('City'),
                      _buildTextField(
                        'Enter city',
                        controller: _cityController,
                      ),

                      _buildLabel('State'),
                      _buildTextField(
                        'Enter state',
                        controller: _stateController,
                      ),

                      // _buildLabel('ZIP Code'),
                      // _buildTextField(
                      //   'Enter ZIP code',
                      //   controller: _zipCodeController,
                      // ),

                      _buildLabel('Landmark Or Other Details'),
                      _buildTextField(
                        'Enter nearby landmark',
                        controller: _landmarkController,
                      ),

                      // _buildLabel('Phone Number'),
                      // _buildTextField(
                      //   'Enter phone number',
                      //   controller: _phoneNumberController,
                      //   isPhoneNumber: true,
                      // ),

                      SizedBox(height: forteen),

                      Container(
                        color: Colors.white,
                        height: MediaQuery.of(context).size.height * 0.1,
                        child: SafeArea(
                          child: SizedBox(
                              width: double.infinity,
                              height: ten * 2.5,
                              child: BlocConsumer<AccountBloc, AccountState>(
                                listener: (context, state) {
                                  if (state is AddAddressSuccess) {
                                    context
                                        .read<AccountBloc>()
                                        .add(ListAddressesEvent());

                                    setState(() {
                                      _isSaving = false;
                                    });

                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(state.message),
                                        backgroundColor: Colors.green,
                                      ),
                                    );
                                    WidgetsBinding.instance
                                        .addPostFrameCallback((_) {
                                      Navigator.pop(context, state.addressData);
                                    });
                                  } else if (state is AddAddressFailed) {
                                    setState(() {
                                      _isSaving = false;
                                    });
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(state.message),
                                        backgroundColor: Colors.red,
                                      ),
                                    );
                                  } else if (state is AddAddressLoading) {
                                    setState(() {
                                      _isSaving = true;
                                    });
                                  } else if (state is EditAddressSuccess) {
                                    setState(() {
                                      _isSaving = false;
                                    });
                                    WidgetsBinding.instance
                                        .addPostFrameCallback((_) {
                                      Navigator.pop(context, state.addressData);
                                    });
                                  } else if (state is EditAddressFailed) {
                                    setState(() {
                                      _isSaving = false;
                                    });
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(state.message),
                                        backgroundColor: Colors.red,
                                      ),
                                    );
                                  } else if (state is EditAddressLoading) {
                                    setState(() {
                                      _isSaving = true;
                                    });
                                  }
                                },
                                builder: (context, state) {
                                  return ElevatedButton(
                                    onPressed:
                                        _isSaving ? null : _onSavePressed,
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: const Color(0xFF1F2122),
                                      shape: RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(twentyFour),
                                      ),
                                      elevation: 0,
                                    ),
                                    child: _isSaving
                                        ? SizedBox(
                                            height: twenty,
                                            width: twenty,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                              color: Colors.white,
                                            ),
                                          )
                                        : Text(
                                            widget.type == 'edit'
                                                ? 'Update Address'
                                                : 'Save Address',
                                            style: TextStyle(
                                              fontFamily: 'Suisse Int\'l',
                                              fontWeight: FontWeight.w400,
                                              fontSize: sixteen,
                                              height: 16 / sixteen,
                                              letterSpacing: 0.02,
                                              color: Colors.white,
                                            ),
                                          ),
                                  );
                                },
                              )),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            // bottomNavigationBar: Container(
            //   color: Colors.white,
            //   padding: EdgeInsets.only(
            //       top: twenty, bottom: ten * 5.5, left: twenty, right: twenty),
            //   child: SafeArea(
            //     child: SizedBox(
            //       width: double.infinity,
            //       height: ten * 5.5,
            //       child: ElevatedButton(
            //         onPressed: _onSavePressed,
            //         style: ElevatedButton.styleFrom(
            //           backgroundColor: const Color(0xFF1F2122),
            //           shape: RoundedRectangleBorder(
            //             borderRadius: BorderRadius.circular(25),
            //           ),
            //           elevation: 0,
            //         ),
            //         child: Text(
            //           widget.type == 'edit' ? 'Update Address' : 'Save Address',
            //           style: TextStyle(
            //             fontFamily: 'Suisse Int\'l',
            //             fontWeight: FontWeight.w400,
            //             fontSize: sixteen,
            //             height: 16 / sixteen,
            //             letterSpacing: 0.02,
            //             color: Colors.white,
            //           ),
            //         ),
            //       ),
            //     ),
            //   ),
            // ),
            // ),
          ),
        );
      }),
    );
  }

  // Method to populate UI with address data
  void _populateAddressData(ViewAddressData addressData) {
    // Populate text fields
    _houseNumberController.text = addressData.houseNumber ?? '';
    _streetAddressController.text = addressData.addressText ?? '';
    _landmarkController.text = addressData.landmark ?? '';

    // Store address ID and current status
    _currentAddressId = addressData.id;
    _isCurrentAddress = addressData.isCurrent ?? false;

    // Set building type
    if (addressData.buildingType != null &&
        buildingTypes.contains(addressData.buildingType)) {
      setState(() {
        buildingType = addressData.buildingType;
      });
    }

    // Set coordinates and update map
    if (addressData.location?.coordinates != null &&
        addressData.location!.coordinates!.length >= 2) {
      final longitude = addressData.location!.coordinates![0];
      final latitude = addressData.location!.coordinates![1];

      currentLatLng = LatLng(latitude, longitude);
      selectedLatitude = latitude;
      selectedLongitude = longitude;

      // Update the marker position
      setState(() {
        _markers.clear();
        _markers.add(
          Marker(
            markerId: const MarkerId('edit_address'),
            position: currentLatLng,
            icon: BitmapDescriptor.defaultMarkerWithHue(
                BitmapDescriptor.hueOrange),
            infoWindow: InfoWindow(
              title: 'Address Location',
              snippet: addressData.addressText ?? 'Selected address',
            ),
          ),
        );
      });

      // Move camera to the loaded location
      _moveCameraToLocation(currentLatLng);

      // Update address from coordinates
      _updateAddress(currentLatLng);
    }
  }

  // Method to move camera to specific location
  Future<void> _moveCameraToLocation(LatLng location) async {
    try {
      final GoogleMapController controller = await _controller.future;
      await controller.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: location,
            zoom: 16,
            tilt: 20.0,
          ),
        ),
      );
    } catch (e) {
      developer.log('Error moving camera: $e');
    }
  }

  // Show confirmation dialog for making address current
  Future<bool?> _showMakeCurrentDialog() async {
    return showDialog<bool>(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            padding: const EdgeInsets.all(20),
            constraints: const BoxConstraints(maxWidth: 400),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header with title and close button
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const SizedBox(width: 24), // Space for symmetry
                    Text(
                      'Set as Current Address',
                      style: TextStyle(
                        fontFamily: 'Suisse Int\'l',
                        fontWeight: FontWeight.w400,
                        fontSize: eighteen,
                        height: 1.28,
                        letterSpacing: 0,
                      ),
                    ),
                    GestureDetector(
                      onTap: () => Navigator.of(context).pop(false),
                      child: const Icon(
                        Icons.close,
                        size: 24,
                        color: Colors.black54,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // Message text
                Text(
                  'Do you want to make this your current address?',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w400,
                    fontSize: forteen,
                    height: 1.43, // 20px / 14px = 1.43
                    letterSpacing: 0,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 32),

                // Action buttons
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.of(context).pop(false),
                        style: OutlinedButton.styleFrom(
                          backgroundColor: Colors.white,
                          side:
                              const BorderSide(color: Colors.black12, width: 1),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(50),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child: Text(
                          'No',
                          style: TextStyle(
                            fontFamily: 'Suisse Int\'l',
                            fontWeight: FontWeight.w400,
                            fontSize: sixteen,
                            height: 1.0,
                            letterSpacing: 0.32, // 2% of 16px
                            color: Colors.black87,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => Navigator.of(context).pop(true),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.black87,
                          foregroundColor: Colors.white,
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(50),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child: Text(
                          'Yes',
                          style: TextStyle(
                            fontFamily: 'Suisse Int\'l',
                            fontWeight: FontWeight.w400,
                            fontSize: sixteen,
                            height: 1.0,
                            letterSpacing: 0.32, // 2% of 16px
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
